* {
    padding: 0;
    margin: 0;
}
:root {
    --primary-color:  var(--primary-color);
    --link-color: var(--primary-color);
    --success-color: var(--primary-color); // 成功色
    --warning-color: #FFA34A; // 警告色
    --error-color: #F5222D; // 错误色
    --font-size-base:  var(--text-color); // 主字号
    --heading-color: var(--text-color); // 标题色
    --text-color: var(--text-color); // 主文本色，
    --suggestive-color: var(--primary-color); // 提示性文字
    --text-color-secondary: var(--text-color); // 次文本色
    --disabled-color:#F0F0F0; // 失效色
    --border-radius-base: 4px; // 组件/浮层圆角
    --border-color-base: #D8D8D8; // 边框色
    --box-shadow-base: #eee; // 浮层阴影
    --body-background: var(--bg-color); //白色
    --gray-background: #f7f8fa; //灰色背景
    --acitve-background: var(--primary-color); //选中颜色
}

li {
    list-style: none;
}

.icon {
    fill: currentColor;
    overflow: hidden;
}

.scrollbar {
    overflow: auto;
    position: relative;
}

.ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: keep-all;
}

.ellipsis2 {
    word-break: break-all;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.ant-btn + .ant-btn {
    margin-left: 12px;
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 2px;
    box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
}

@media print {
    @page {
        margin: 0;
    }
}

.table_backgroundcolor_worning {
    background-color: #fff6ea;
    .ant-table-cell-row-hover {
        background: #fff6ea !important;
    }
}

.throttle {
    animation: throttle 1.5s step-end forwards;
}
.throttle:active {
    animation: none;
}
@keyframes throttle {
    from {
        pointer-events: none;
    }

    to {
        pointer-events: all;
    }
}

// 隐藏Edge浏览器密码显示图标
input::-ms-reveal,
input::-ms-clear {
    display: none;
}

// 隐藏input的type为search的清空按钮
input[type='search']::-webkit-search-decoration,
input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-results-button,
input[type='search']::-webkit-search-results-decoration {
    -webkit-appearance: none;
}
