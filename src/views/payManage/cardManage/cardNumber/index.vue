<template>
    <div class="card_number">
        <div class="card_number_head">
            <ArrowLeftOutlined @click="back" :style="{ color: 'var(--primary-color)', fontSize: '16px' }" />
            <span class="page_title">卡号管理</span>
        </div>
        <div class="card_number_content">
            <search-form style="margin-bottom: 20px" v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset" />
            <div class="btn_group">
                <a-button type="primary" @click="giveCardFn">发卡</a-button>
                <a-button>导入</a-button>
                <a-button @click="delectCard">删除</a-button>
                <a-button @click="distributeCardRecords">发卡记录</a-button>
            </div>
            <div class="table_box">
                <ETable :columns="columns" :data-source="dataSource" :paginations="pagination" @change="handleTableChange">
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        <template v-if="column.dataIndex == 'majorName'">{{ record.majorName }}</template>
                    </template>
                </ETable>
            </div>
        </div>
        <!-- 发卡记录 -->
        <give-card-records ref="giveCardRecordsRef" />

        <!-- 发卡 -->
        <give-card ref="giveCardRef" />
    </div>
</template>

<script setup>
import { message, Modal } from "ant-design-vue"
import { createVNode } from "vue"
import { useRouter } from "vue-router"
import { ExclamationCircleFilled } from "@ant-design/icons-vue"

import GiveCard from "../components/giveCard.vue"
import GiveCardRecords from "./components/giveCardRecords.vue"
const router = useRouter()

const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = ref([
    { title: "序号", dataIndex: "index", width: 100 },
    { title: "卡号", dataIndex: "majorName", key: "majorName", width: 100 },
    { title: "卡片状态", dataIndex: "educationalSystem", key: "educationalSystem", width: 100 },
    { title: "操作人", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "操作时间", dataIndex: "createTime", key: "createTime", width: 160 }
])
const formList = ref([
    {
        type: "select",
        value: "machineStatus",
        label: "卡片状态",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    }
])

const giveCardRecordsRef = ref(null)
const giveCardRef = ref(null)

// 发卡
function giveCardFn() {
    giveCardRef.value.open()
}

function delectCard() {
    Modal.confirm({
        title: "提示",
        icon: createVNode(ExclamationCircleFilled),
        content: "确定删除吗？",
        okText: "确 认",
        cancelText: "取 消",
        onOk() {
            // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
            // http.post(url, form.value)
            //     .then((res) => {
            //         YMessage.success(res.message)
            //         cancel()
            //         emit("submitDrawer")
            //     })
            //     .finally(() => {
            //         submitLoading.value = false
            //     })
        },
        onCancel() {
            message.info("已取消！")
        }
    })
}

// 发卡记录
function distributeCardRecords() {
    giveCardRecordsRef.value.open()
}

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

function back() {
    router.back()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.card_number {
    .card_number_head {
        padding: 18px 20px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        flex: 1;
        .page_title {
            margin-left: 10px;
            font-weight: 600;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }
    }
    .card_number_content {
        padding: 14px 20px;
        .btn_group {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 12px;
        }
        .table_box {
            width: 100%;
        }
    }
}
</style>
