<template>
    <!-- 金额卡片区域 -->
    <div class="card_list">
        <div class="card_item" v-for="item in 4" :key="item">
            <div class="card_value">{{ "1212" }}笔</div>
            <div class="card_title">今日订单数</div>
        </div>
    </div>
    <!-- 搜索组件区域 -->
    <search-form
        style="margin-bottom: 20px"
        v-model:formState="query"
        :formList="formList"
        @submit="getInitList"
        layout="horizontal"
        @reset="reset"
    />

    <div class="table_box">
        <!-- 表格 -->
        <ETable
            :columns="columns"
            :data-source="dataSource"
            :paginations="pagination"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                <template v-if="column.dataIndex == 'operate'">
                    <a-button type="link" class="btn-link-color" @click="addDrawerFn('edit')"
                        >退款</a-button
                    >
                </template>
            </template>
        </ETable>
    </div>
</template>

<script setup>
const columns = ref([
    { title: "序号", dataIndex: "index", width: 100 },
    { title: "收款商户", dataIndex: "majorName", key: "majorName" },
    { title: "外部订单号", dataIndex: "educationalSystem", key: "educationalSystem" },
    { title: "交易流水号", dataIndex: "createBy", key: "createBy" },
    { title: "商品", dataIndex: "createTime", key: "createTime" },
    { title: "合计金额", dataIndex: "createTime", key: "createTime" },
    { title: "创建订单时间", dataIndex: "createTime", key: "createTime" },
    { title: "订单状态", dataIndex: "createTime", key: "createTime" },
    { title: "支付渠道", dataIndex: "createTime", key: "createTime" },
    { title: "支付设备", dataIndex: "createTime", key: "createTime" },
    { title: "支付时间", dataIndex: "createTime", key: "createTime" },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

// 模拟商品数据
const mockCommodityData = {
    id: 1,
    name: "香草拿铁",
    barcode: "1234567890123",
    categoryId: 1,
    price: 25.5,
    stock: 100,
    status: 1,
    description: "香浓的香草拿铁，选用优质咖啡豆，口感丝滑，香味浓郁。",
    imageUrl: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
}

const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "内部订单号"
    },
    {
        type: "input",
        value: "majorName",
        label: "交易流水号"
    },
    {
        type: "select",
        value: "machineStatus",
        label: "支付渠道",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "创建订单时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "支付时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])
</script>

<style lang="less" scoped>
.card_list {
    display: flex;
    align-items: center;

    margin-bottom: 20px;
    .card_item {
        margin-right: 12px;
        width: 110px;
        height: 70px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #e8e8e8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .card_value {
            padding-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
            color: #262626;
        }
        .card_title {
            font-weight: 400;
            font-size: 12px;
            color: #595959;
        }
    }
}
</style>
