/* 初始化 */
a,
abbr,
acronym,
address,
applet,
area,
article,
aside,
audio,
b,
base,
basefont,
bdi,
bdo,
big,
blockquote,
body,
br,
button,
canvas,
caption,
center,
cite,
code,
col,
colgroup,
datalist,
dd,
del,
details,
dir,
div,
dfn,
dialog,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
font,
footer,
form,
frame,
frameset,
h1,
h2,
h3,
h4,
h5,
h6,
head,
header,
hr,
html,
i,
iframe,
img,
input,
ins,
isindex,
kbd,
keygen,
label,
legend,
li,
link,
map,
mark,
menu,
menuitem,
meta,
meter,
nav,
noscript,
object,
ol,
optgroup,
option,
output,
p,
param,
pre,
progress,
q,
rp,
rt,
ruby,
s,
samp,
script,
section,
select,
small,
source,
span,
strike,
strong,
style,
sub,
summary,
sup,
table,
tbody,
td,
textarea,
tfoot,
th,
thead,
time,
title,
tr,
track,
tt,
u,
ul,
var,
video,
wbr,
xmp {
	box-sizing: border-box;
	padding: 0;
	margin: 0;
}

*::before,
*::after {
	box-sizing: border-box;
	padding: 0;
	margin: 0;
}

body {
	font: 16px/1 "PingFang SC", "Microsoft YaHei", sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

dl,
li,
menu,
ol,
ul {
	list-style: none;
}

button,
input,
select,
textarea {
	outline: none;
}

a,
a:link,
a:visited,
a:hover,
a:active {
	text-decoration: none;
}

/* 浮动方式 */
.fl {
	float: left;
}

.fr {
	float: right;
}

.clear {
	clear: both;
	overflow: hidden;
	height: 0;
	font-size: 0;
}

.clearfix::after {
	display: block;
	clear: both;
	height: 0;
	font-size: 0;
	visibility: hidden;
	content: "";
}

/* 定位方式 */
.pr {
	position: relative;
}

.pa {
	position: absolute;
}

.pf {
	position: fixed;
}

.center {
	margin: 0 auto;
}

/* 对齐方式 */
.tal {
	text-align: left;
}

.tac {
	text-align: center;
}

.tar {
	text-align: right;
}

.taj {
	text-align: justify;
}

/* 居中定位 */
.abs-ct {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.abs-cx {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.abs-cy {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
}

/* 弹性布局 */
.flex-ct-x {
	display: flex;
	justify-content: center;
	align-items: center;
}

.flex-ct-y {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}

.flex-fs {
	display: flex;
	flex-wrap: wrap;
	place-content: space-between space-between;
}

/* 动画模式 */
.td-camera {
	perspective: 1000;
}

.td-space {
	transform-style: preserve-3d;
}

.td-box {
	backface-visibility: hidden;
}

.gpu-speed {
	transform: translate3d(0, 0, 0);
}

/* 其他 */
.fullscreen {
	inset: 0;
}

.ellipsis {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.page-at {
	overflow: auto;
	width: 100%;
	height: 100%;
}

.page-fs {
	overflow: hidden;
	width: 100%;
	height: 100%;
}

.round {
	color: red;
	border-radius: 100%;
}

*, *::before, *::after {
    box-sizing: border-box;
}
