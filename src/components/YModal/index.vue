<template>
    <a-modal v-model:open="state.open" :maskClosable="false" :destroyOnClose="true" v-bind="$attrs" @cancel="cancel" @ok="confirm">
        <template #title>
            <slot></slot>
        </template>
        <div class="y-modal-container">
            <slot></slot>
        </div>
        <template #footer v-if="slot.footer">
            <slot name="footer"></slot>
        </template>
    </a-modal>
</template>

<script setup>
const slot = useSlots()

// *********************
// Hooks Function
// *********************
const props = defineProps({
    open: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(["update:open", "cancel", "confirm"])

const state = reactive({
    open: false
})

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const cancel = () => {
    state.open = false
    emit("update:open", state.open)
    emit("cancel")
}

const confirm = () => {
    emit("confirm")
}

// *********************
// Watch Function
// *********************

watch(
    () => props.open,
    (open) => {
        state.open = open
    },
    {
        immediate: true,
        deep: true
    }
)
</script>

<style lang="less" scoped>
.y-modal-container {
    overflow-y: auto;
    max-height: 606px;
}
</style>
