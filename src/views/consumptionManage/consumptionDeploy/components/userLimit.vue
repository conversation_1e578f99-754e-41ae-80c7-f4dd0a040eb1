<template>
    <div>
        <YModal v-model:open="openModal" title="人员消费限制" @close="cancel" @cancel="cancel" @confirm="submit" :width="860">
            <div class="content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item label="状态：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择状态' }]">
                                <a-radio-group v-model:value="form.v2alue" name="radioGroup">
                                    <a-radio value="1">开启</a-radio>
                                    <a-radio value="2">关闭</a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="日限次：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入日限次' }]">
                                <a-input-number style="width: 100%" :step="1" :precision="2" v-model:value.trim="form.major1Name" placeholder="请输入"> </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="月限次：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入月限次' }]">
                                <a-input-number style="width: 100%" :step="1" :precision="2" v-model:value.trim="form.majorName" placeholder="请输入"> </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="日限额：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入日限额' }]">
                                <a-input-number style="width: 100%" :step="1" :precision="2" v-model:value.trim="form.major1Name" placeholder="请输入"> </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="月限额：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入月限额' }]">
                                <a-input-number style="width: 100%" :step="1" :precision="2" v-model:value.trim="form.majorName" placeholder="请输入"> </a-input-number>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="备注说明：" name="majorName">
                                <a-textarea v-model:value.trim="form.majorName" placeholder="请输入" :auto-size="{ minRows: 10, maxRows: 10 }" show-count :maxlength="500" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YModal>
    </div>
</template>

<script setup>
const emit = defineEmits(["submitDrawer"])
const openModal = ref(false)
const submitLoading = ref(false)
const modalType = ref("add")

const formRef = ref(null)
const form = ref({})

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
        // http.post(url, form.value)
        //     .then((res) => {
        //         YMessage.success(res.message)
        //         cancel()
        //         emit("submitDrawer")
        //     })
        //     .finally(() => {
        //         submitLoading.value = false
        //     })
        emit("submitDrawer")
    })
}

function open(type, item) {
    modalType.value = type
    openModal.value = true
    console.log(item)
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
}
</style>
