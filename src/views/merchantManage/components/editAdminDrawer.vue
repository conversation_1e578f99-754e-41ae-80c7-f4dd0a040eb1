<template>
    <div>
        <a-drawer :width="500" title="编辑" placement="right" :open="drawerOpen" @close="cancel">
            <div>
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item label="管理员：" name="name" :rules="[{ required: true, trigger: 'blur', message: '请输入管理员' }]">
                                <!-- show-count :maxlength="20" -->
                                <a-input v-model:value.trim="form.name" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="手机号：" name="phone" :rules="[{ required: true, trigger: 'blur', message: '请输入手机号' }]">
                                <a-input v-model:value.trim="form.phone" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </a-drawer>
    </div>
</template>

<script setup>
const emit = defineEmits(["submitDrawer"])
const drawerOpen = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})

function open(item) {
    drawerOpen.value = true
    form.value = item || {}
}

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        // const url = drawerType.value == 'edit' ? '/cloud/enrollment/major/update' : '/cloud/enrollment/major/create'
        // http.post(url, form.value)
        //     .then(res => {
        //         YMessage.success(res.message)
        //         cancel()
        //         emit('submitDrawer')
        //     })
        //     .finally(() => {
        //         submitLoading.value = false
        //     })
        emit("submitDrawer")
    })
}

function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="scss" scoped></style>
