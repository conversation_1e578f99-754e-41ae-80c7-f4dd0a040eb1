<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="商户详情" @close="cancel" :footer="null">
            <div class="drawer_content">
                <div class="info_content">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="item.span" v-for="item in infoLabel" :key="item.key">
                            <div v-if="item.key === 'splitLine'" class="split_line"></div>
                            <div v-else-if="item.key === 'name'">
                                <div class="title_merchant">
                                    {{ info[item.key] }}
                                    <div class="merchant_grade">二级商户</div>
                                </div>
                                <div class="merchant_status">开启</div>
                            </div>
                            <div v-else class="info">{{ item.label }}：</div>
                        </a-col>
                    </a-row>
                </div>
                <div class="record_content">
                    <div class="record_title">更新记录：</div>
                    <ETable :columns="columns" :data-source="dataSource" :paginations="pagination" @change="handleTableChange"> </ETable>
                </div>
            </div>
        </YDrawer>
    </div>
</template>

<script setup>
const drawerOpen = ref(false)

const info = {
    id: "id",
    name: "name",
    area: "area",
    system: "system",
    title1: "title1",
    title21: "title21",
    titl1e: "titl1e",
    ti1tle: "ti1tle"
}

const infoLabel = [
    {
        key: "name",
        label: "商户名称",
        span: 24
    },
    {
        key: "id",
        label: "商户ID",
        span: 8
    },
    {
        key: "area",
        label: "所在地区",
        span: 8
    },
    {
        key: "system",
        label: "关联系统",
        span: 8
    },
    {
        key: "splitLine",
        label: "分割线",
        span: 24
    },
    {
        key: "title1",
        label: "负责人",
        span: 8
    },
    {
        key: "title21",
        label: "负责人电话",
        span: 8
    },
    {
        key: "titl1e",
        label: "创建人",
        span: 8
    },
    {
        key: "ti1tle",
        label: "更新时间",
        span: 8
    }
]
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = ref([
    { title: "序号", dataIndex: "index" },
    { title: "用户", dataIndex: "majorName", key: "majorName" },
    { title: "更新内容", dataIndex: "educationalSystem", key: "educationalSystem" },
    { title: "更新时间", dataIndex: "createBy", key: "createBy" }
])

function open() {
    drawerOpen.value = true
    getList()
}
function cancel() {
    drawerOpen.value = false
}

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
    .info_content {
        min-height: 201px;
        background: #f6f6f6;

        padding: 16px;
        border-radius: 4px;
        position: relative;
        .split_line {
            height: 1px;
            background: #e6e6e6;
            width: 100%;
        }
        .title_merchant {
            font-weight: 500;
            font-size: 18px;
            color: var(--text-color);
            line-height: 22px;
            display: flex;
            align-items: center;
            .merchant_grade {
                background: var(--primary-color-bg);
                border-radius: 4px;
                border: 1px solid var(--primary-color);
                padding: 4px;
                font-weight: 400;
                font-size: 10px;
                color: var(--primary-color);
                line-height: 12px;
                margin-left: 6px;
            }
        }
        .merchant_status {
            position: absolute;
            top: -16px;
            right: -4px;
            width: 55px;
            height: 24px;
            background: var(--primary-color);
            border-radius: 0px 4px 0px 13px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ffffff;
        }
        .info {
            color: var(--text-color);
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
        }
    }
    .record_content {
        .record_title {
            font-weight: 500;
            font-size: 16px;
            margin: 16px 0;
            color: var(--text-color);
            line-height: 22px;
        }
    }
}
</style>
