<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="微信收款配置" @close="cancel">
            <div class="drawer_content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <!--  show-count :maxlength="50"  -->
                            <a-form-item label="小程序APPID：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择小程序APPID' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="小程序Appsecret：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入小程序Appsecret' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="公众号APPID:" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入公众号APPID' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="服务商名称：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入服务商名称' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="服务商mchid：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '服务商mchid' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="服务商API证书序列号：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入服务商API证书序列号' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="服务商API v3密钥：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入服务商API v3密钥' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="服务商支付证书私钥：" name="fileList" :rules="[{ required: true, trigger: 'blur', message: '请上传服务商支付证书私钥' }]">
                                <a-image :width="200" v-if="form.url" :src="form.url" alt="服务商支付证书私钥">
                                    <template #previewMask>
                                        <div class="image_preview_mask">
                                            <EyeOutlined style="margin-right: 10px" />
                                            <div @click.stop="delectImage">
                                                <DeleteOutlined />
                                            </div>
                                        </div>
                                    </template>
                                </a-image>
                                <a-upload v-else ref="uploads" accept=".jpg, .jpeg, .png, .JPG, .JPEG" action="/" :multiple="false" :show-upload-list="false" :file-list="form.fileList" :limit="1" :before-upload="(file) => beforeUpload(file)">
                                    <a-button type="primary" ghost style="background: var(--primary-color-bg)">上传附件</a-button>
                                </a-upload>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="服务商支付类型：" name="majorNameList" :rules="[{ required: true, trigger: 'blur', message: '请选择服务商支付类型' }]">
                                <a-checkbox-group v-model:value="form.majorNameList" :options="payTypeOptions" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="子商户mchid：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入子商户mchid' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="子商户APPID:" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入子商户APPID' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YDrawer>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const emit = defineEmits(["submitDrawer"])
const drawerOpen = ref(false)

const submitLoading = ref(false)

const formRef = ref(null)
const form = ref({})

const payTypeOptions = ref([
    {
        label: "小程序支付",
        value: "miniprogram"
    },
    {
        label: "公众号支付",
        value: "wxpay"
    }
])

// 上传图片
const uploadImages = async (file) => {
    http.postForm("/file/common/upload", { file, folderType: "enrollmentReport" })
        .then((res) => {
            form.value.fileList = res.data
            form.value.url = res.data[0]?.url
        })
        .catch(() => {
            message.error("图片上传失败!")
        })
}

// 判断图片大小以及转成base64
const beforeUpload = (file) => {
    const imageSize = file.size / 1024 / 1024
    if (imageSize > 1) {
        message.error(`图片大小为${imageSize.toFixed(2)}M, 大于${1}M`)
        return false
    }
    uploadImages(file)
    return false
}

function delectImage() {
    form.value.fileList = []
    form.value.url = ""
}

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
        // http.post(url, form.value)
        //     .then((res) => {
        // message.success(res.message)
        //         cancel()
        //         emit("submitDrawer")
        //     })
        //     .finally(() => {
        //         submitLoading.value = false
        //     })
        emit("submitDrawer")
    })
}

function open() {
    drawerOpen.value = true
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
}
.image_preview_mask {
    display: flex;
}
</style>
