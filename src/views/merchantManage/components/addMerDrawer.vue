<template>
    <div>
        <YDrawer v-model:open="drawerOpen" :title="drawerTitle" @close="cancel">
            <div class="drawer_content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item label="商户类型：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择商户类型' }]">
                                <a-select
                                    ref="select"
                                    v-model:value="form.isNonlocalStudent"
                                    placeholder="请选择"
                                    :options="[
                                        { label: '是', value: 1 },
                                        { label: '否', value: 0 }
                                    ]"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="商户名称：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入商户名称' }]">
                                <a-input v-model:value.trim="form.majorName" show-count :maxlength="50" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="所在地区：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择所在地区' }]">
                                <a-cascader :allowClear="false" v-model:value="form.nativePlaces" :options="cityOptions" placeholder="请选择" :fieldNames="{ label: 'name', value: 'id', children: 'area' }" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="详细地址：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入详细地址' }]">
                                <a-input v-model:value.trim="form.majorName" show-count :maxlength="100" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="负责人：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入负责人' }]">
                                <a-input v-model:value.trim="form.majorName" show-count :maxlength="50" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="负责人电话：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入负责人电话' }]">
                                <a-input v-model:value.trim="form.majorName" show-count :maxlength="11" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YDrawer>
    </div>
</template>

<script setup>
const emit = defineEmits(["submitDrawer"])
const drawerOpen = ref(false)
const drawerType = ref("add")
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})
const drawerTitle = computed(() => {
    const titleObj = {
        add: "新增",
        edit: "编辑",
        info: "查看"
    }
    return `${titleObj[drawerType.value]}商户`
})

const cityOptions = ref([])

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
        // http.post(url, form.value)
        //     .then((res) => {
        //         YMessage.success(res.message)
        //         cancel()
        //         emit("submitDrawer")
        //     })
        //     .finally(() => {
        //         submitLoading.value = false
        //     })
        emit("submitDrawer")
    })
}

function open(type, item) {
    drawerOpen.value = true
    drawerType.value = type || "add"
    form.value = type === "add" ? {} : item || {}
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
}
</style>
