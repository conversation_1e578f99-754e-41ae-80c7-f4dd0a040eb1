<template>
    <div>
        <div class="card_manage" v-if="route.path === '/payManage/cardManage'">
            <div class="left_people">
                <people-tree @changeKey="changeKey" />
            </div>
            <div class="right_content">
                <!-- 头部 -->
                <div class="card_head">
                    <span class="title">卡片操作记录</span>
                    <div class="btn_group">
                        <a-button type="primary" @click="giveCardFn">发卡</a-button>
                        <a-button @click="cardNumFn">卡号管理</a-button>
                        <a-button @click="cardOperate">卡片操作</a-button>
                    </div>
                </div>
                <div class="content_page">
                    <!-- 搜索 -->
                    <search-form style="margin-bottom: 20px" v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset" />
                    <div class="table_box">
                        <!-- 表格 -->
                        <!--  :scroll="{ x: 800 }" -->
                        <ETable :columns="columns" :data-source="dataSource" :paginations="pagination" @change="handleTableChange">
                            <template #bodyCell="{ column, record, index }">
                                <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                                <template v-else-if="column.dataIndex == 'operate'">
                                    <a-button type="link" class="btn-link-color" @click="cardInfoFn(record)">详情</a-button>
                                    <a-button type="link" class="btn-link-color" @click="editCardFn(record)">编辑</a-button>
                                </template>
                            </template>
                        </ETable>
                    </div>
                </div>
            </div>

            <!-- 编辑卡片 -->
            <edit-card ref="editCardRef" />

            <!-- 卡片详情 -->
            <card-info ref="cardInfoRef" />

            <!-- 发卡 -->
            <give-card ref="giveCardRef" />
        </div>
        <router-view></router-view>
    </div>
</template>

<script setup>
import GiveCard from "./components/giveCard.vue"
import EditCard from "./components/editCard.vue"
import CardInfo from "./components/cardInfo.vue"
import PeopleTree from "./components/peopleTree.vue"
import { useRouter, useRoute } from "vue-router"

const router = useRouter()
const route = useRoute()
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = ref([
    { title: "工号", dataIndex: "index", width: 100 },
    { title: "姓名", dataIndex: "majorName", key: "majorName", width: 100 },
    { title: "性别", dataIndex: "educationalSystem", key: "educationalSystem", width: 100 },
    { title: "所在班级", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "用户状态", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "卡片状态", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "余额", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "使用期限", dataIndex: "createTime", key: "createTime", width: 160 },
    { title: "更新时间", dataIndex: "createTime", key: "createTime", width: 160 },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])
const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "姓名"
    },
    {
        type: "input",
        value: "majorName",
        label: "所属班级"
    },
    {
        type: "select",
        value: "machineStatus",
        label: "卡片状态",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "input",
        value: "majorName",
        label: "卡号"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "更新时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const editCardRef = ref(null)
const cardInfoRef = ref(null)
const giveCardRef = ref(null)

// 发卡
function giveCardFn() {
    giveCardRef.value.open()
}

// 卡片操作
function cardOperate() {
    router.push({
        path: "/payManage/cardManage/cardOperate"
    })
}

// 卡号管理
function cardNumFn() {
    router.push({
        path: "/payManage/cardManage/cardNumber"
    })
}

// 编辑卡片
function editCardFn(record) {
    console.log("editCard", record)
    editCardRef.value.open()
}

// 卡片详情
function cardInfoFn(record) {
    console.log("cardInfoFn", record)
    cardInfoRef.value.open()
}

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

function changeKey(ids, item) {
    console.log("changeKey", ids, item)
}
onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.card_manage {
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .left_people {
        min-width: 252px;
        width: 252px;
        min-height: 100%;
        border-right: 1px solid #d8d8d8;
    }
    .right_content {
        flex: 1;
        .card_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-weight: 500;
                font-size: 18px;
                color: var(--text-color);
                line-height: 25px;
            }
            .btn_group {
                display: flex;
            }
        }
        .content_page {
            padding: 20px;
            width: 100%;
            .table_box {
                width: 100%;
            }
        }
    }
}
</style>
