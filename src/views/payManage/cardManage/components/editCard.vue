<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="编辑" @close="cancel">
            <div class="drawer_content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="12">
                            <a-form-item label="学号：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入学号' }]">
                                <a-input :disabled="true" v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="姓名：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入姓名' }]">
                                <a-input :disabled="true" v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="性别：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择性别' }]">
                                <a-select
                                    ref="select"
                                    :disabled="true"
                                    v-model:value="form.isNonlocalStudent"
                                    placeholder="请选择"
                                    :options="[
                                        { label: '男', value: 1 },
                                        { label: '女', value: 0 }
                                    ]"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="所在部门：" :disabled="true" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入所在部门' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="卡号：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入卡号' }]">
                                <a-input :disabled="true" v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="卡片状态：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择卡片状态' }]">
                                <a-input :disabled="true" v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="余额：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入余额' }]">
                                <a-input :disabled="true" v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="卡片类型：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择卡片类型' }]">
                                <a-input :disabled="true" v-model:value.trim="form.majorName" placeholder="请输入" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="消费类型：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择消费类型' }]">
                                <a-select
                                    ref="select"
                                    mode="multiple"
                                    v-model:value="form.isNonlocalStudent"
                                    placeholder="请选择"
                                    :options="[
                                        { label: '一级选项五', value: 1 },
                                        { label: '一级选项1', value: 0 }
                                    ]"
                                ></a-select>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="卡有效期：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择卡有效期' }]">
                                <RangePicker v-model:startTime="form.startTime" v-model:endTime="form.endTime"></RangePicker>
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="卡片押金：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入卡片押金' }]">
                                <a-input-number style="width: 100%" :step="1" :precision="2" v-model:value.trim="form.majorName" placeholder="请输入" addon-after="元"> </a-input-number>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
            <template #footer>
                <a-button @click="cancel">取消</a-button>
                <a-button type="primary" :loading="submitLoading" @click="submit">确定</a-button>
            </template>
        </YDrawer>
    </div>
</template>

<script setup>
const emit = defineEmits(["submitDrawer"])
const drawerOpen = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})

function submit() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
        // http.post(url, form.value)
        //     .then((res) => {
        //         YMessage.success(res.message)
        //         cancel()
        //         emit("submitDrawer")
        //     })
        //     .finally(() => {
        //         submitLoading.value = false
        //     })
        emit("submitDrawer")
    })
}

function open(item) {
    drawerOpen.value = true
    form.value = item || {}
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
}
</style>
