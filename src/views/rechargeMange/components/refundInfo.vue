<template>
    <div>
        <YModal v-model:open="openModal" title="退款详情" @close="cancel" @cancel="cancel" :footer="null" :width="700">
            <div class="content">
                <a-form :model="form" ref="formRef" layout="horizontal">
                    <a-form-item label="退款原因：" name="majorName">
                        <span>退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。退学需要申请退款。</span>
                    </a-form-item>
                    <a-form-item name="majorName">
                        <a-radio-group v-model:value="form.v2alue" name="radioGroup" :disabled="true">
                            <a-radio value="1">同意</a-radio>
                            <a-radio value="2">拒绝</a-radio>
                        </a-radio-group>
                    </a-form-item>
                    <a-form-item label="审核原因：" name="majorName">
                        <span>已过退款日期。</span>
                    </a-form-item>
                </a-form>
            </div>
        </YModal>
    </div>
</template>

<script setup>
const openModal = ref(false)

const form = ref({})

function open() {
    openModal.value = true
}
function cancel() {
    openModal.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.content {
    padding: 20px;
}
</style>
