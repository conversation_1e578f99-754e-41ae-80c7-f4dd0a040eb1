<template>
    <div class="page_content">
        <div class="content_box">
            <div class="gatherHead">
                <a-tabs v-model:activeKey="state.activeKey" style="flex: 1">
                    <a-tab-pane key="Shop" tab="超市消费订单"></a-tab-pane>
                    <a-tab-pane key="Refund" tab="退款订单"></a-tab-pane>
                </a-tabs>
            </div>
            <div class="bodyRightBox">
                <component :is="moduleList[state.activeKey]"></component>
            </div>
        </div>
    </div>
</template>

<script setup>
const state = ref({
    activeKey: "Shop"
})

const moduleList = {
    Shop: defineAsyncComponent(() => import("./component/shop.vue")), // 消费
    Refund: defineAsyncComponent(() => import("./component/refund.vue")) // 退款
}
</script>

<style lang="less" scoped>
.page_content {
    background: #ffffff;
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .content_box {
        flex: 1;
    }
}

.bodyRightBox {
    padding: 16px;
}
.gatherHead {
    border-bottom: 1px solid #d9d9d9;
    height: 54px;
    padding-left: 16px;
    padding-right: 16px;
    display: flex;
    align-items: center;
    :deep(.ant-tabs-nav) {
        height: 54px !important;
        margin: 0;
        font-size: 16px;
        &::before {
            border: none !important;
        }
        .ant-tabs-tab {
            font-size: 16px !important;
            padding: unset !important;
            font-weight: 600;
            & + .ant-tabs-tab {
                margin: 0 0 0 40px;
            }
        }
    }
}
</style>
