<template>
    <div>
        <!-- v-model:expandedKeys="expandedKeys" -->
        <slot name="handleFooter"></slot>
        <a-tree v-if="treeData.length" v-model:selectedKeys="selectedKeyss" :tree-data="treeData" showIcon :disabled="disabled" :checkable="checkable" blockNode :draggable="isDraggable" :fieldNames="fieldNames" :selectable="true" :defaultExpandAll="defaultExpandAll" @dragenter="onDragEnter" @drop="onDrop" @select="handlerSelect" v-bind="$attrs">
            <template #title="{ dataRef }">
                <slot name="treeIcon" :treeItem="{ id: dataRef.id, name: dataRef.name, item: dataRef }">
                    <i class="iconfont icon-yellow" :class="treeIcon"></i>
                </slot>
                <Tooltip :title="aliasEndName(dataRef)"></Tooltip>
                <slot name="handleItem" :key="dataRef.id" :handleItem="dataRef"></slot>
            </template>
        </a-tree>

        <Empty v-if="!treeData.length && isShowEmpty" :emptyStyle="{ marginTop: '50%' }" :tips="emptyTitle"></Empty>
    </div>
</template>
<script>
import { defineComponent, ref, toRaw, computed, nextTick } from "vue"
import { useRoute, useRouter } from "vue-router"
import { getCurNodeParents } from "./data"

export default defineComponent({
    props: {
        isShowEmpty: {
            type: Boolean,
            default: true
        },
        defaultExpandAll: {
            type: Boolean,
            default: true
        },
        treeData: {
            type: Array,
            default: () => {
                return []
            }
        },
        fieldNames: {
            type: Object,
            default: () => {
                return {
                    children: "list",
                    title: "name",
                    key: "id"
                }
            }
        },
        isDraggable: {
            type: Boolean,
            default: false
        },
        treeIcon: {
            type: String,
            default: "icon-a-Fill21"
        },
        defaultExpand: {
            type: Array,
            default: () => {
                return []
            }
        },
        selectedKeys: {
            type: Array,
            default: () => {
                return []
            }
        },
        emptyTitle: {
            type: String,
            default: "暂无数据"
        },
        checkable: {
            // 勾选框
            type: Boolean,
            default: false
        },
        disabled: {
            // 禁用
            type: Boolean,
            default: false
        }
    },
    components: {},
    setup(props, { emit }) {
        const router = useRouter()
        const route = useRoute()
        const expandedKeys = ref([])
        const draggable = ref(false)
        const titleObj = ref({})

        const selectedKeyss = computed(() => {
            return props.selectedKeys
        })

        // const treeIcon = ref<string>(props.treeIcon)
        const handlerSelect = (selectedKeyss, item) => {
            if (draggable.value) return
            if (selectedKeyss.length) {
                emit("emitSelect", selectedKeyss[0], item.node)
                const { ancestors, type, showName, name, rollValue } = item.node
                router.push({
                    path: route.path,
                    query: {
                        ...route.query,
                        deptId: selectedKeyss[0],
                        title: showName || name,
                        type,
                        ancestors,
                        rollValue
                    }
                })
            }
        }
        const onDragEnter = (info) => {
            if (props.isDraggable) {
                draggable.value = true
                console.log(info, "info")
            }
        }
        // 可拖拽
        const onDrop = (info) => {
            if (props.isDraggable) {
                draggable.value = false
                emit("emitDraggable", info)
            }
        }
        // // 默认展开
        // watch(
        //     () => props.defaultExpand,
        //     (val) => {
        //         expandedKeys.value = val
        //         selectedKeyss.value = props.selectedKeys
        //     }
        // )

        nextTick(() => {
            if (!props.defaultExpand.length) {
                const id = props.selectedKeys[0] || (props.treeData[0] && props.treeData[0][props.fieldNames.key])
                const { parentNodeIds } = getCurNodeParents(toRaw(props.treeData), id) || {}
                expandedKeys.value = parentNodeIds ? [...parentNodeIds, ...selectedKeyss.value] : [...selectedKeyss.value]
            } else {
                expandedKeys.value = props.defaultExpand
            }
        })

        const aliasEndName = computed(() => {
            return (dataRef) => {
                const name = dataRef[props.fieldNames.title] || dataRef.name
                const names = dataRef.name
                const Id = dataRef[props.fieldNames.key]
                titleObj.value[Id] = names
                return name
            }
        })

        return {
            expandedKeys,
            selectedKeyss,
            draggable,
            titleObj,
            handlerSelect,
            onDrop,
            onDragEnter,
            aliasEndName
        }
    }
})
</script>
<style lang="less" scoped>
:deep(.ant-tree-block-node) {
    margin-bottom: 18px;
}

:deep(.ant-tree-switcher-icon) {
    font-size: 12px;
    vertical-align: middle;
}

:deep(.ant-tree-title) {
    display: flex !important;
}

:deep(.icon-yellow) {
    font-size: 12px;
    color: var(--warning-color);
    margin-right: 8px;
}

:deep(.ant-select-tree-node-selected) {
    padding: 5px 4px !important;
}

// 小三角
:deep(.ant-tree-switcher) {
    width: 16px;
    text-align: right;
    line-height: 32px !important;
}

// 三点 样式
:deep(.handle_icon) {
    position: absolute;
    right: 0;
    color: rgba(0, 0, 0, 0.65);
    display: none;
}

:deep(.demp_tree) {
    padding: 24px 16px;
    border-right: 1px solid #d8d8d8;
    box-sizing: border-box;
    width: 276px;
    // height: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

// 树的左侧距离
:deep(.ant-tree-indent-unit, .ant-select-tree-indent-unit) {
    width: 10px;
}
</style>
<style lang="less">
.ant-dropdown-menu-item:hover .ant-btn,
.ant-dropdown-menu-submenu-title:hover .ant-btn {
    background: transparent;
}

.ant-select-tree-treenode-selected,
.ant-tree-treenode {
    padding: 0 !important;
    width: 100%;
    position: relative;
}

.multi-function-item:hover,
.ant-select-tree-treenode:hover,
.ant-tree-treenode:hover {
    background-color: #f6f6f6ff !important;
}

.ant-tree-treenode-selected:hover {
    .handle_icon {
        display: inline-block;
    }
}

.multi-function-item.active,
.ant-select-tree-treenode-selected,
.ant-tree-treenode-selected {
    background-color: var(--primary-color-bg) !important;

    .ant-tree-node-selected {
        color: var(--primary-color);

        .icon-yellow {
            color: var(--primary-color);
        }

        .handle_icon {
            display: inline-block;
        }
    }
}
// 一行自适应隐藏
.ant-select-tree-title,
.ant-tree-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 0;
}
.ant-select-tree-node-selected,
.ant-tree-node-content-wrapper,
.ant-tree-node-selected {
    background-color: transparent !important;
    padding: 5px 8px !important;
    display: flex;
    flex: 1;
}
</style>
