<template>
    <div class="empty" :style="emptyStyle">
        <img src="@/assets/images/empty.png" alt="" srcset="" />
        <p class="tips">{{ tips }}</p>
    </div>
</template>

<script setup>
defineProps({
    tips: {
        type: String,
        default: "暂无数据"
    },
    emptyStyle: {
        type: Object,
        default: () => {
            return {}
        }
    }
})
</script>

<style scoped lang="less">
.empty {
    text-align: center;
    margin: 0 auto;

    img {
        width: 200px;
        height: 180px;
    }

    .tips {
        color: #bdbcbc;
    }
}
</style>
