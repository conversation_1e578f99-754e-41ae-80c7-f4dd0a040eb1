<template>
    <div class="container">
        <searchForm v-model:formState="state.form" :formList="formList" layout="horizontal" @submit="queryInitData" @reset="queryInitData"></searchForm>

        <ETable style="margin-top: 16px" :columns="columns" :minH="450" :scroll="{ x: 1500 }" :data-source="state.dataSource" :paginations="state.pagination" :loading="state.loading" @change="handleTableChange">
            <template #bodyCell="{ column, text, record, index }">
                <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
                <template v-else-if="column.dataIndex === 'businessJson'">
                    {{ JSON.parse(record.businessJson)?.planName }}
                </template>
                <template v-else-if="column.dataIndex === 'operate'">
                    <a-button type="link" class="btn-link-color" @click="handleRefundReviewOpen(record)">
                        <!-- 0.审核中 1.审核通过 2.审核失败 3.退款中 4.退款成功 5.退款失败 -->
                        {{ record.orderStatus ? "详情" : "退款审核" }}
                    </a-button>
                </template>
                <Tooltip v-else :maxWidth="column.width" :title="text"></Tooltip>
            </template>
        </ETable>

        <YModal v-model:open="state.refundReviewOpen" :title="state.refundReviewForm.orderStatus ? '退款详情' : '退款审核'" width="700px" :bodyStyle="{ padding: '24px' }" @cancel="handleRefundReviewCancel">
            <a-form class="refund-form" :model="state.refundReviewForm" ref="refundReviewRef" layout="vertical">
                <a-form-item name="refundR" class="refund-reason" style="margin: 0">
                    <template #label>
                        <div class="reset-label">
                            <span class="refund-reason-label">退款原因：</span>
                            <span class="refund-reason-text">
                                {{ state.refundReviewForm.refundReason }}
                            </span>
                        </div>
                    </template>
                </a-form-item>
                <a-form-item name="auditStatus" style="margin-bottom: 10px">
                    <a-radio-group v-model:value="state.refundReviewForm.auditStatus" :disabled="!!state.refundReviewForm.orderStatus">
                        <a-radio :value="1">同意</a-radio>
                        <a-radio :value="2">拒绝</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="审核原因：" name="auditRemarks" :rules="[{ required: true, message: '请输入说明原因!' }]">
                    <a-textarea class="refund-textarea" v-model:value.trim="state.refundReviewForm.auditRemarks" :disabled="!!state.refundReviewForm.orderStatus" placeholder="请说明原因" show-count :maxlength="100" :auto-size="{ minRows: 8, maxRows: 5 }" />
                </a-form-item>
            </a-form>
            <template #footer>
                <a-button key="back" @click="handleRefundReviewCancel">取消</a-button>
                <a-button key="submit" type="primary" v-if="!state.refundReviewForm.orderStatus" :loading="state.refundReviewLoading" @click="handleRefundReviewOk"> 确定 </a-button>
            </template>
        </YModal>
    </div>
</template>

<script setup>
import { message } from "ant-design-vue"
const refundReviewRef = ref(null)
const state = reactive({
    loading: false,
    form: {},
    dataSource: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    sort: {
        field: "",
        order: ""
    },
    // 3.智慧点餐 4.招生迎新 ，null：查询所有
    businessType: 4,
    refundReviewLoading: false,
    refundReviewOpen: false,
    refundReviewForm: {
        id: "",
        auditRemarks: "",
        auditStatus: 1, // 1.同意 2.拒绝
        orderStatus: 1 // 0.退款审核 1.退款审核记录
    }
})

const formList = computed(() => {
    return [
        {
            type: "input",
            value: "refundNo",
            label: "退款编号"
        },
        {
            type: "input",
            value: "orderUserName",
            label: "类型"
        },
        {
            type: "input",
            value: "orderUserName",
            label: "姓名"
        },
        {
            type: "input",
            value: "payPhone",
            label: "卡号"
        },
        {
            type: "rangePicker",
            label: "申请退款时间",
            value: ["cra teTime", "endTime"],
            voidValue: "crateEndTime",
            span: 7,
            attrs: {
                valueFormat: "YYYY-MM-DD",
                format: "YYYY-MM-DD",
                showTime: true
            }
        },
        {
            type: "rangePicker",
            label: "退款到账时间",
            value: ["startRefundTime", "endRefundTime"],
            voidValue: "startEndRefundTime",
            span: 7,
            attrs: {
                valueFormat: "YYYY-MM-DD",
                format: "YYYY-MM-DD",
                showTime: true
            }
        }
    ]
})

const columns = [
    { title: "序号", dataIndex: "index", width: 80 },
    { title: "退款编号", dataIndex: "refundNo" },
    { title: "姓名", dataIndex: "orderUserName" },
    { title: "卡号", dataIndex: "payPhone" },
    { title: "金额", dataIndex: "refundAmount" },
    { title: "申请退款时间", dataIndex: "createTime", sorter: true },
    { title: "退款去向", dataIndex: "refundDestination" },
    // { title: "退款原因", dataIndex: "refundReason" },
    { title: "退款状态", dataIndex: "orderStatusName" },
    { title: "退款到账时间", dataIndex: "refundTime", sorter: true, width: 180 },
    { title: "操作", dataIndex: "operate", fixed: "right", width: 80 }
]

const handleRefundReviewCancel = () => {
    refundReviewRef.value.resetFields()
    state.refundReviewForm.auditStatus = 1
    state.refundReviewForm.auditRemarks = ""
    state.refundReviewOpen = false
}

// 提交退款审核
const handleRefundReviewOk = () => {
    refundReviewRef.value.validate().then(() => {
        state.refundReviewLoading = true
        http.post("/campuspay/general-mgmt/refund-order/refund-audit", state.refundReviewForm)
            .then(({ msg }) => {
                message.success(msg)
                handleRefundReviewCancel()
            })
            .finally(() => {
                state.refundReviewLoading = false
            })
    })
}

const handleRefundReviewOpen = (item) => {
    state.refundReviewOpen = true
    state.refundReviewForm.orderStatus = item.orderStatus
    state.refundReviewForm.refundReason = item.refundReason || ""
    state.refundReviewForm.id = item.id
    // 如果退款状态为0 则为审核记录，否则为退款审核
    if (item.orderStatus) {
        handleReviewDetail()
    }
}

// 审核详情
const handleReviewDetail = () => {
    http.post("/campuspay/general-mgmt/refund-order/detail", { id: state.refundReviewForm.id }).then(({ data }) => {
        state.refundReviewForm = data
    })
}

// 常规支付-退款订单-分页
const getInitData = () => {
    state.loading = true
    const params = {
        ...state.form,
        ...state.pagination,
        businessType: state.businessType
    }
    if (state.sort.field) {
        params.field = state.sort.field
        params.order = state.sort.order
    }
    http.post("/campuspay/general-mgmt/refund-order/refundOrderPage", params)
        .then(({ data }) => {
            const { pageNo, pageSize, total, list } = data
            state.dataSource = list
            state.pagination.pageNo = pageNo
            state.pagination.pageSize = pageSize
            state.pagination.total = total
        })
        .finally(() => {
            state.loading = false
        })
    state.dataSource = [{ name: 1, orderStatus: true }]
}
// 分页
const handleTableChange = ({ current, pageSize, total }, filters, { field, order }) => {
    state.pagination.pageNo = current
    state.pagination.pageSize = pageSize
    state.pagination.total = total
    state.sort.field = ""
    state.sort.order = ""
    if (order) {
        state.sort.field = field === "createTime" ? "create_time" : "refund_time"
        state.sort.order = order === "ascend" ? "asc" : "desc"
    }
    getInitData()
}

// 查询 重置
const queryInitData = () => {
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    getInitData()
}

onMounted(() => {
    getInitData()
})
</script>

<style lang="less" scoped>
.container {
    padding: 20px;
}
</style>
