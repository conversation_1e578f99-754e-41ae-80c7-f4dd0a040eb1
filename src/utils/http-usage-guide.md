# HTTP 请求工具使用指南

## 概述

这个 HTTP 工具基于 Axios 封装，提供了完整的 HTTP 请求功能，包括：
- 自动取消重复请求
- 请求重试机制
- 数据缓存
- Token 自动刷新
- 错误处理和提示
- 文件上传下载
- 多种请求格式支持

## 基本使用

### 导入方式

```javascript
// 导入 HTTP 实例
import http from '@/utils/http'
```

## API 方法详解

### 1. GET 请求

```javascript
// 基本 GET 请求
const response = await http.get('/api/users')

// 带参数的 GET 请求
const response = await http.get('/api/users', {
  page: 1,
  size: 10,
  name: '张三'
})

// 带配置的 GET 请求
const response = await http.get('/api/users',
  { page: 1 },
  {
    loading: true,           // 显示加载动画
    loadingText: '加载中...', // 自定义加载文字
    cache: true,             // 启用缓存
    maxAge: 5000            // 缓存5秒
  }
)
```

### 2. POST 请求 (JSON 格式)

```javascript
// 基本 POST 请求
const response = await http.post('/api/users', {
  name: '张三',
  age: 25,
  email: '<EMAIL>'
})

// 带配置的 POST 请求
const response = await http.post('/api/users',
  { name: '张三' },
  {
    loading: true,
    successMessage: true,    // 成功后显示提示
    retryTimes: 3,          // 失败重试3次
    retryDelay: 1000        // 重试间隔1秒
  }
)
```

### 3. 表单提交 (form-data)

```javascript
// 表单数据提交
const formData = {
  name: '张三',
  avatar: fileObject,      // 文件对象
  tags: ['前端', '开发']    // 数组数据
}

const response = await http.postForm('/api/users', formData, {
  loading: true,
  loadingText: '提交中...'
})
```

### 4. URL 编码表单提交

```javascript
// application/x-www-form-urlencoded 格式
const response = await http.postUrlEncoded('/api/login', {
  username: 'admin',
  password: '123456'
})
```

### 5. 文件上传

```javascript
// 单文件上传
const fileInput = document.querySelector('#file-input')
const file = fileInput.files[0]

const response = await http.upload('/api/upload', file,
  {
    userId: 123,           // 额外的表单数据
    category: 'avatar'
  },
  {
    loading: true,
    onUploadProgress: (progressEvent) => {
      const progress = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      )
      console.log(`上传进度: ${progress}%`)
    }
  }
)
```

### 6. 文件下载

```javascript
// 下载文件（自动获取文件名）
await http.download('/api/download/report.pdf')

// 下载文件（指定文件名）
await http.download('/api/download/123', '用户报告.pdf', {
  loading: true,
  loadingText: '下载中...'
})
```

### 7. 其他 HTTP 方法

```javascript
// PUT 请求
const response = await http.put('/api/users/123', {
  name: '李四',
  age: 30
})

// DELETE 请求
const response = await http.delete('/api/users/123')

// PATCH 请求
const response = await http.patch('/api/users/123', {
  age: 31
})
```

## 高级配置

### 自定义请求头

```javascript
// 单次请求自定义请求头
const response = await http.post('/api/data',
  { data: 'test' },
  {
    headers: {
      'Custom-Header': 'custom-value',
      'Content-Type': 'application/xml'  // 覆盖默认的 JSON 格式
    }
  }
)
```

### 完全自定义请求

```javascript
// 使用 request 方法进行完全自定义
const response = await http.request({
  method: 'POST',
  url: '/api/custom',
  data: { test: 'data' },
  headers: {
    'Authorization': 'Bearer custom-token',
    'Content-Type': 'application/json'
  },
  timeout: 10000,
  loading: true,
  retryTimes: 2
})
```

## 配置选项说明

### HttpRequestConfig 接口

```typescript
interface HttpRequestConfig {
  // === Axios 原生配置 ===
  method?: string           // 请求方法
  url?: string             // 请求地址
  data?: any               // 请求数据
  params?: any             // URL 参数
  headers?: any            // 请求头
  timeout?: number         // 超时时间
  responseType?: string    // 响应类型
  
  // === 扩展配置 ===
  loading?: boolean        // 是否显示加载动画
  loadingText?: string     // 加载动画文字
  successMessage?: boolean // 成功后是否显示提示
  retryTimes?: number      // 重试次数
  retryDelay?: number      // 重试延迟时间(毫秒)
  cache?: boolean          // 是否启用缓存(仅GET请求)
  maxAge?: number          // 缓存时间(毫秒)
  forceUpdate?: boolean    // 强制更新缓存
}
```

## 内置功能

### 1. 自动 Token 管理
- 自动从 localStorage 读取 token 并添加到请求头
- Token 过期时自动刷新
- 刷新失败时自动跳转登录页

### 2. 重复请求取消
- 自动检测并取消重复的请求
- 支持白名单配置

### 3. 错误处理
- HTTP 状态码错误自动处理
- 业务错误码自动处理
- 错误信息自动提示

### 4. 请求缓存
- GET 请求支持缓存
- 可配置缓存时间
- 支持强制更新缓存

### 5. 请求重试
- 网络错误自动重试
- 可配置重试次数和间隔

## 使用示例

### 用户管理模块

```javascript
// api/user.js
import { httpRequest } from '@/utils/http'

export const userApi = {
  // 获取用户列表
  getUserList: (params) => httpRequest.get('/api/users', params, {
    cache: true,
    maxAge: 30000  // 缓存30秒
  }),
  
  // 创建用户
  createUser: (data) => httpRequest.post('/api/users', data, {
    loading: true,
    successMessage: true
  }),
  
  // 更新用户头像
  updateAvatar: (userId, file) => httpRequest.upload(
    `/api/users/${userId}/avatar`, 
    file,
    {},
    {
      loading: true,
      loadingText: '上传头像中...'
    }
  ),
  
  // 导出用户数据
  exportUsers: () => httpRequest.download('/api/users/export', '用户数据.xlsx', {
    loading: true,
    loadingText: '导出中...'
  })
}
```

### 在 Vue 组件中使用

```vue
<template>
  <div>
    <button @click="loadUsers">加载用户</button>
    <button @click="createUser">创建用户</button>
    <input type="file" @change="uploadAvatar" />
  </div>
</template>

<script setup>
import { userApi } from '@/api/user'

// 加载用户列表
const loadUsers = async () => {
  try {
    const users = await userApi.getUserList({ page: 1, size: 10 })
    console.log('用户列表:', users)
  } catch (error) {
    console.error('加载失败:', error)
  }
}

// 创建用户
const createUser = async () => {
  try {
    await userApi.createUser({
      name: '新用户',
      email: '<EMAIL>'
    })
    // 成功提示会自动显示
  } catch (error) {
    // 错误提示会自动显示
  }
}

// 上传头像
const uploadAvatar = async (event) => {
  const file = event.target.files[0]
  if (file) {
    try {
      await userApi.updateAvatar(123, file)
    } catch (error) {
      console.error('上传失败:', error)
    }
  }
}
</script>
```

## 注意事项

1. **默认请求头**: 所有请求默认使用 `Content-Type: application/json`
2. **Token 管理**: Token 会自动添加到请求头，无需手动设置
3. **错误处理**: 网络错误和业务错误都会自动处理并提示
4. **文件上传**: 使用 `postForm` 或 `upload` 方法，会自动设置正确的 Content-Type
5. **缓存机制**: 只对 GET 请求有效，其他请求方法不会缓存
6. **重复请求**: 相同的请求会被自动取消，避免重复提交

这个 HTTP 工具提供了完整的请求功能，可以满足大部分业务需求。如有特殊需求，可以使用 `request` 方法进行完全自定义配置。
