<template>
    <div class="merchant_manage">
        <div class="header">商户管理</div>
        <div class="content">
            <search-form v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset" />
            <div class="btn_group">
                <a-button type="primary" @click="addDrawerFn('add', null)">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新增商户
                </a-button>
                <a-button type="primary" ghost @click="moneyConfig">收款配置</a-button>
            </div>
            <ETable :columns="columns" :scroll="{ x: 1500 }" :data-source="dataSource" :paginations="pagination" @change="handleTableChange">
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                    <template v-else-if="column.dataIndex == 'operate'">
                        <a-button type="link" class="btn-link-color" @click="infoDrawerFn(record)">详情</a-button>
                        <a-button type="link" class="btn-link-color" @click="addDrawerFn('edit', record)">编辑</a-button>
                        <a-button type="link" class="btn-link-color" @click="statusDeviceFn(record)">开启</a-button>
                        <a-button type="link" class="btn-link-color" @click="lookDeviceFn(record)">查看设备</a-button>
                        <a-button type="link" class="btn-link-color" @click="merAdminFn(record)">商户管理员</a-button>
                    </template>
                </template>
            </ETable>
        </div>

        <!-- 新增编辑商户 -->
        <AddMerDrawer ref="addMerDrawerRef" @submitDrawer="reset" />

        <!-- 商户详情 -->
        <MerInfoDrawer ref="merInfoDrawerRef" />

        <!-- 收款配置 -->
        <MoneyConfig ref="moneyConfigRef" />

        <!-- 商户管理员 -->
        <MerchantAdmin ref="merAdminRef" />
    </div>
</template>

<script setup>
import MerchantAdmin from "./components/merchantAdmin.vue"
import MoneyConfig from "./components/moneyConfig.vue" // 收款配置
import MerInfoDrawer from "./components/merInfoDrawer.vue" // 商户详情
import AddMerDrawer from "./components/addMerDrawer.vue" // 新增商户
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = ref([
    { title: "商户ID", dataIndex: "index", width: 100 },
    { title: "商户名称", dataIndex: "majorName", key: "majorName", width: 150 },
    { title: "所在地", dataIndex: "educationalSystem", key: "educationalSystem", width: 200 },
    { title: "商户类型", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "商户状态", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "更新时间", dataIndex: "createTime", key: "createTime", width: 160 },
    { title: "操作", dataIndex: "operate", width: 210, fixed: "right" }
])
const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "商户ID"
    },
    {
        type: "input",
        value: "majorName",
        label: "商户名称"
    },
    {
        type: "input",
        value: "majorName",
        label: "所在地址"
    },
    {
        type: "select",
        value: "machineStatus",
        label: "商户类型",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "select",
        value: "machineStatus",
        label: "商户状态",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "input",
        value: "majorName",
        label: "负责人"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "更新时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const addMerDrawerRef = ref(null)
const merInfoDrawerRef = ref(null)
const moneyConfigRef = ref(null)
const merAdminRef = ref(null)

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

function reset() {
    query.value = {}
    getInitList()
}

// 收款配置
function moneyConfig() {
    moneyConfigRef.value.open()
}

// 新增编辑
function addDrawerFn(type, record = {}) {
    addMerDrawerRef.value.open(type, deepClone(record))
}

// 详情
function infoDrawerFn(record) {
    console.log("详情", record)
    merInfoDrawerRef.value.open(record)
}

// 商户状态
function statusDeviceFn() {
    console.log("商户状态")
}

// 查看设备
function lookDeviceFn() {
    console.log("查看设备")
}

// 商户管理员
function merAdminFn() {
    merAdminRef.value.open()
}

onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.merchant_manage {
    .header {
        padding: 18px 20px;
        font-weight: 500;
        font-size: 18px;
        color: #262626;
        line-height: 25px;
        border-bottom: 1px solid #d9d9d9;
    }

    .content {
        padding: 20px;

        .btn_group {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 16px;
        }
    }
}
</style>
