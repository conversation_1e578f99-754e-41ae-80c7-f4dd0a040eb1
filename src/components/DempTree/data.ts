/**
 * @description: 获取当前传入节点得所有父节点
 * @param {Array} tree tree数组
 * @param {string} id tree数组
 * @param {Object} options 配置对象
 * @param {Object} children tree结构得children字段 - 默认children
 * @param {Object} key tree结构得唯一标识 - 默认id
 * @param {Object} parent tree结构父级标识 - 默认pid
 * @return {Object} 返回所有父节点,及id
 */

// 树形结构默认字段
const treeDefaultFieldNames = {
    children: "children",
    key: "id",
    parent: "pid"
}

export function getCurNodeParents(tree: [], id: string | number, options: object = {}) {
    if (!Array.isArray(tree)) {
        throw new Error("The first parameter of getcurnodeparts requires an array")
    }
    if (!id) {
        return
    }
    const _options = Object.assign(treeDefaultFieldNames, options)
    const parent: Array<string | number> = []
    const node: Array<{}> = []
    const forFn = function (arr: [], id) {
        for (let i = 0; i < arr.length; i++) {
            const item = arr[i]
            if (item[_options.key] === id) {
                // TODO: 当前业务顶级pid为 0，顶级时则需要使用唯一标识
                parent.push(item[_options.parent] === 0 ? item[_options.key] : item[_options.parent])
                node.push(item)
                forFn(tree, item[_options.parent])
                break
            } else {
                if (item[_options.children]) {
                    forFn(item[_options.children], id)
                }
            }
        }
    }
    forFn(tree, id)
    return {
        parentNodes: node,
        parentNodeIds: parent
    }
}
