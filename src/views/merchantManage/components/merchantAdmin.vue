<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="商户详情" @close="cancel" :footer="null">
            <div>
                <search-form v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset" />
                <div style="margin-top: 20px">
                    <ETable :columns="columns" :data-source="dataSource" :paginations="pagination" @change="handleTableChange">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.dataIndex == 'operate'">
                                <a-button type="link" class="btn-link-color" @click="editAdmin(record)">编辑</a-button>
                                <a-button type="link" class="btn-link-color" @click="statusAdmin(record)">禁用</a-button>
                                <a-button type="link" class="btn-link-color" @click="resetPassword(record)">重置密码</a-button>
                            </template>
                        </template>
                    </ETable>
                </div>
            </div>
        </YDrawer>
        <EditAdminDrawer ref="editAdminDrawerRef" />
    </div>
</template>

<script setup>
import EditAdminDrawer from "./editAdminDrawer.vue"
const drawerOpen = ref(false)

const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const editAdminDrawerRef = ref(null)

const formList = ref([
    {
        type: "input",
        value: "name",
        label: "管理员"
    },
    {
        type: "input",
        value: "phone",
        label: "手机号码"
    },
    {
        type: "select",
        value: "status",
        label: "账号状态",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    }
])

const columns = ref([
    { title: "ID", dataIndex: "index" },
    { title: "管理员", dataIndex: "majorName", key: "majorName" },
    { title: "手机号", dataIndex: "educationalSystem", key: "educationalSystem" },
    { title: "帐号状态", dataIndex: "createBy", key: "createBy" },
    { title: "更新时间", dataIndex: "createBy", key: "createBy" },
    { title: "操作", dataIndex: "operate", key: "operate", width: 220, fixed: "right" }
])

// 编辑
function editAdmin() {
    console.log("编辑")
    editAdminDrawerRef.value.open()
}

// 修改状态
function statusAdmin() {
    console.log("修改状态")
}

// 重置密码
function resetPassword() {
    console.log("重置密码")
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

function reset() {
    query.value = {}
    getInitList()
}

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function open() {
    drawerOpen.value = true
    getList()
}
function cancel() {
    drawerOpen.value = false
}

defineExpose({ open })
</script>

<style lang="less" scoped></style>
