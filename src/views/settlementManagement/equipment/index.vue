<template>
    <div class="page_content">
        <div class="content_box">
            <!-- 头部 -->
            <div class="card_head">
                <span class="title">设备结算报表</span>
            </div>
            <div class="content_page">
                <!-- 搜索组件区域 -->
                <search-form
                    style="margin-bottom: 20px"
                    v-model:formState="query"
                    :formList="formList"
                    @submit="getInitList"
                    layout="horizontal"
                    @reset="reset"
                />

                <!-- 按钮区域 -->
                <div class="btn_group">
                    <a-button>导出</a-button>
                </div>
                <div class="table_box">
                    <!-- 表格 -->
                    <ETable
                        :columns="columns"
                        :data-source="dataSource"
                        :paginations="pagination"
                        @change="handleTableChange"
                    >
                        <template #bodyCell="{ column, record, index }"> </template>
                    </ETable>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useRouter, useRoute } from "vue-router"

const router = useRouter()
const route = useRoute()

const commodityDrawerRef = ref(null)
const commodityTypeDrawerRef = ref(null)
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})
const columns = ref([
    { title: "数据日期", dataIndex: "majorName", key: "majorName" },
    { title: "设备IMEI", dataIndex: "educationalSystem", key: "educationalSystem" },
    { title: "设备名称", dataIndex: "createBy", key: "createBy" },
    { title: "支付总金额", dataIndex: "createTime", key: "createTime" },
    { title: "支付总笔数", dataIndex: "createTime", key: "createTime" },
    { title: "刷卡支付笔数", dataIndex: "createTime", key: "createTime" },
    { title: "刷卡总金额", dataIndex: "createTime", key: "createTime" },
    { title: "刷脸总金额", dataIndex: "createTime", key: "createTime" },
    { title: "刷脸总金额", dataIndex: "createTime", key: "createTime" },
    { title: "现金支付笔数", dataIndex: "createTime", key: "createTime" },
    { title: "现金支付总金额", dataIndex: "createTime", key: "createTime" }
])

// 模拟商品数据
const mockCommodityData = {
    id: 1,
    name: "香草拿铁",
    barcode: "1234567890123",
    categoryId: 1,
    price: 25.5,
    stock: 100,
    status: 1,
    description: "香浓的香草拿铁，选用优质咖啡豆，口感丝滑，香味浓郁。",
    imageUrl: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"
}

const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "设备名称"
    },
    {
        type: "input",
        value: "majorName",
        label: "设备编号"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "数据日期",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const editCardRef = ref(null)
const cardInfoRef = ref(null)
const giveCardRef = ref(null)

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

// 打开抽屉
const addDrawerFn = (type, record = {}) => {
    if (type === "add") {
        // 新建商品
        commodityDrawerRef.value?.open("add")
    } else if (type === "edit") {
        // 编辑商品
        commodityDrawerRef.value?.open("edit", mockCommodityData)
    } else if (type === "detail") {
        // 商品详情
        commodityDrawerRef.value?.open("detail", mockCommodityData)
    }
}

// 处理商品抽屉提交事件
const handleSubmitDrawer = () => {
    // 刷新商品列表
    getInitList()
}
onMounted(() => {
    reset()
})
</script>

<style lang="less" scoped>
.page_content {
    background: #ffffff;
    width: 100%;
    max-width: 100%;
    min-height: calc(100vh - 120px);
    display: flex;
    .content_box {
        flex: 1;
        .card_head {
            width: 100%;
            padding: 16px 20px;
            border-bottom: 1px solid #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .title {
                font-weight: 500;
                font-size: 18px;
                color: var(--text-color);
                line-height: 25px;
            }
        }
        .content_page {
            padding: 20px;
            width: 100%;
            .btn_group {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                margin-bottom: 16px;
            }
            .table_box {
                width: 100%;
            }
        }
    }
}
</style>
