<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="发卡记录" @close="cancel" :footer="null">
            <div class="drawer_content">
                <a-radio-group style="margin-bottom: 12px" v-model:value="peopleType" button-style="solid" @change="getList">
                    <a-radio-button value="teacher"> <div class="radio_item">老师</div> </a-radio-button>
                    <a-radio-button value="student"> <div class="radio_item">学生</div> </a-radio-button>
                </a-radio-group>
                <search-form style="margin-bottom: 20px" v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset" />
                <div class="btn_group">
                    <a-button>导出</a-button>
                </div>
                <ETable :columns="columns" :data-source="dataSource" :paginations="pagination" @change="handleTableChange">
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                        <template v-if="column.dataIndex == 'majorName'">{{ record.majorName }}</template>
                    </template>
                </ETable>
            </div>
        </YDrawer>
    </div>
</template>

<script setup>
import { computed } from "vue"
const drawerOpen = ref(false)
const peopleType = ref("teacher")
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = computed(() => {
    return [
        { title: "序号", dataIndex: "index", width: 100 },
        { title: "姓名", dataIndex: "majorName", key: "majorName", width: 100 },
        { title: "性别", dataIndex: "majorName", key: "majorName", width: 100 },
        { title: peopleType.value === "teacher" ? "所在部门" : "所在班级", dataIndex: "majorName", key: "majorName", width: 100 },
        { title: "发卡人", dataIndex: "educationalSystem", key: "educationalSystem", width: 100 },
        { title: "发卡时间", dataIndex: "createBy", key: "createBy", width: 100 }
    ]
})
const formList = computed(() => {
    return [
        {
            type: "input",
            value: "majorName",
            label: "姓名"
        },
        {
            type: "input",
            value: "majorName",
            label: peopleType.value === "teacher" ? "所在部门" : "所在班级"
        },
        {
            type: "input",
            value: "majorName",
            label: "卡号"
        },
        {
            type: "rangePicker",
            value: ["startTime", "endTime"],
            label: "发卡时间",
            attrs: {
                placeholder: ["开始时间", "结束时间"],
                valueFormat: "YYYY-MM-DD"
            }
        }
    ]
})

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}
function open() {
    drawerOpen.value = true
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    // max-width: 1200px;
    // margin: auto;
    .btn_group {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 12px;
    }
    .radio_item {
        min-width: 100px;
        text-align: center;
    }
}
</style>
