<!DOCTYPE html>
<html lang="zh">
    <head>
        <meta charset="UTF-8" />
        <meta name="theme-color" content="#fff" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
        <%- version %> <%- description %> <%- keywords %> <%- dnsPrefetch%> <%- icon %>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="stylesheet" href="https://file.1d1j.cn/siderIcon/iconfont.css" />
        <script src="https://file.1d1j.cn/siderIcon/iconfont.js"></script>
        <link rel="stylesheet" href="//at.alicdn.com/t/c/font_3184698_2lkrrswwhha.css" class="src" />
        <!-- 3.0 -->
        <link rel="stylesheet" href="//at.alicdn.com/t/c/font_4160969_7hhyl9c3w4q.css" />
        <title><%- title %></title>
    </head>
    <body>
        <loading-card tip="加载中，请稍等..." spinning="show"></loading-card>
        <div id="app"></div>
        <%- injectScript %>
        <script src="/loading.js"></script>
        <script type="module" src="src/main.ts"></script>
        <script>
            window.addEventListener("error", (e) => {
                const app = window.document.querySelector("#app")
                if (app.children.length == 0) {
                    console.log(`%c【ERROR】渲染错误： ${e.message}`, "color:red")
                    console.log(`%c【ERROR】渲染错误： ${e.filename}`, "color:red")
                }
            })
        </script>
    </body>
</html>
