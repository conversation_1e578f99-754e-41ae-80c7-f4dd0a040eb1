<!--
 * @Descripttion: 注册页
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2023-03-07 10:22:35
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-03-30 18:11:15
-->
<template>
    <div class="register">register</div>
</template>

<script setup lang="ts">
// import { login } from "@/api"
onMounted(() => {
    console.log("register")

    // login({
    //     username: "",
    //     password: ""
    // })
    //     .then((res) => {
    //         console.log(res)
    //     })
    //     .catch(() => {})
})
</script>

<style lang="less" scoped></style>
