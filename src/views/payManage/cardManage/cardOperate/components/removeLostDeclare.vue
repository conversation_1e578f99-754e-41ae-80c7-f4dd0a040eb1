<template>
    <div>
        <!-- 搜索 -->
        <search-form v-model:formState="query" :formList="formList" @submit="getInitList" layout="horizontal" @reset="reset" />
        <div class="btn_group">
            <a-button type="primary" @click="removeLostDeclare">解挂</a-button>
        </div>
        <div class="table_box">
            <!-- 表格 -->
            <ETable :columns="columns" :scroll="{ x: 1200 }" :data-source="dataSource" :paginations="pagination" @change="handleTableChange">
                <template #bodyCell="{ column, record, index }">
                    <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                    <template v-else-if="column.dataIndex == 'majorName'">
                        {{ record.majorName }}
                    </template>
                </template>
            </ETable>
        </div>
        <YModal v-model:open="openModal" title="解挂" @cancel="cancelModal" :width="1024" @confirm="submitModal">
            <div class="report_loss_content">
                <a-form :model="form" ref="formRef" layout="vertical">
                    <a-row :gutter="[24, 18]">
                        <a-col :span="24">
                            <a-form-item label="选择人员：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请选择人员' }]">
                                <a-input v-model:value.trim="form.majorName" placeholder="请选择" />
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="挂失记录：">
                                <ETable :columns="personnelColumns" :scroll="{ x: 800 }" :data-source="personnelDataSource">
                                    <template #bodyCell="{ column, record, index }">
                                        <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                                        <template v-else-if="column.dataIndex == 'majorName'">
                                            {{ record.majorName }}
                                        </template>
                                    </template>
                                </ETable>
                            </a-form-item>
                        </a-col>
                        <a-col :span="24">
                            <a-form-item label="解挂原因：" name="majorName" :rules="[{ required: true, trigger: 'blur', message: '请输入解挂原因' }]">
                                <a-textarea v-model:value.trim="form.majorName" placeholder="请输入" :auto-size="{ minRows: 10, maxRows: 10 }" />
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
            </div>
        </YModal>
    </div>
</template>

<script setup>
const query = ref({})
const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const openModal = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const form = ref({})
const personnelDataSource = ref([])

const columns = ref([
    { title: "工号/学号", dataIndex: "index", width: 100 },
    { title: "姓名", dataIndex: "majorName", key: "majorName", width: 100 },
    { title: "性别", dataIndex: "educationalSystem", key: "educationalSystem", width: 100 },
    { title: "人员类型", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "所在组织", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "卡号", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "卡片状态", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "渠道", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "原因", dataIndex: "createTime", key: "createTime", width: 160 },
    { title: "操作人", dataIndex: "createTime", key: "createTime", width: 160 },
    { title: "操作时间", dataIndex: "createTime", key: "createTime", width: 160 }
])
const formList = ref([
    {
        type: "input",
        value: "majorName",
        label: "姓名"
    },
    {
        type: "input",
        value: "majorName",
        label: "所属组织"
    },
    {
        type: "select",
        value: "machineStatus",
        label: "人员类型",
        list: [
            { label: "全部", value: null },
            { label: "在线", value: 1 },
            { label: "离线", value: 0 }
        ],
        fieldNames: {
            label: "label",
            value: "value"
        }
    },
    {
        type: "input",
        value: "majorName",
        label: "卡号"
    },
    {
        type: "rangePicker",
        value: ["startTime", "endTime"],
        label: "操作时间",
        attrs: {
            placeholder: ["开始时间", "结束时间"],
            valueFormat: "YYYY-MM-DD"
        }
    }
])

const personnelColumns = ref([
    { title: "工号", dataIndex: "index", width: 100 },
    { title: "姓名", dataIndex: "majorName", key: "majorName", width: 100 },
    { title: "性别", dataIndex: "educationalSystem", key: "educationalSystem", width: 100 },
    { title: "所在部门", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "卡号", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "挂失状态", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "挂失渠道", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "挂失原因", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "操作人", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "操作时间", dataIndex: "createTime", key: "createTime", width: 100 }
])

// 弹框确认
function submitModal() {
    formRef.value.validate().then(() => {
        submitLoading.value = true
        // const url = drawerType.value == "edit" ? "/cloud/enrollment/major/update" : "/cloud/enrollment/major/create"
        // http.post(url, form.value)
        //     .then((res) => {
        //         YMessage.success(res.message)
        //         cancel()
        //            openModal.value = false
        //     })
        //     .finally(() => {
        //         submitLoading.value = false
        //     })
        openModal.value = false
    })
}

// 弹框取消
function cancelModal() {
    openModal.value = false
}

// 解挂
function removeLostDeclare() {
    openModal.value = true
}

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}
function reset() {
    query.value = {}
    getInitList()
}

defineExpose({ reset })
</script>

<style lang="less" scoped>
.btn_group {
    display: flex;
    justify-content: flex-end;
    margin: 20px 0;
}
.report_loss_content {
    padding: 20px;
}
</style>
