<template>
    <div>
        <YDrawer v-model:open="drawerOpen" title="详情" @close="cancel" :footer="null">
            <div class="drawer_content">
                <div class="title">
                    <div class="line"></div>
                    基本信息
                </div>
                <a-row :gutter="[24, 18]">
                    <a-col :span="8" v-for="item in infoLabel" :key="item.key"> {{ item.label }} {{ info[item.key] || "-" }} </a-col>
                </a-row>
                <div class="record">
                    <a-radio-group v-model:value="recordType" button-style="solid" @change="getInitList">
                        <a-radio-button value="operate">卡片操作记录</a-radio-button>
                        <a-radio-button value="consume">卡片消费记录</a-radio-button>
                    </a-radio-group>
                    <div style="margin-top: 16px">
                        <ETable :columns="columns" :scroll="{ x: 900 }" :data-source="dataSource" :paginations="pagination" @change="handleTableChange">
                            <template #bodyCell="{ column, record, index }">
                                <template v-if="column.dataIndex == 'index'">{{ index + 1 }}</template>
                                <template v-else-if="column.dataIndex == 'operate'">
                                    <a-button type="link" class="btn-link-color" @click="cardRecordInfoFn(record)">详情</a-button>
                                </template>
                            </template>
                        </ETable>
                    </div>
                </div>
            </div>
        </YDrawer>
    </div>
</template>

<script setup>
const drawerOpen = ref(false)
const infoLabel = [
    {
        label: "学号：",
        key: "name"
    },
    {
        label: "姓名：",
        key: "name"
    },
    {
        label: "性别：",
        key: "name"
    },
    {
        label: "所在部门：",
        key: "name"
    },
    {
        label: "卡片状态：",
        key: "name"
    },
    {
        label: "卡号：",
        key: "name"
    },
    {
        label: "余额：",
        key: "name"
    },
    {
        label: "卡片有效期：",
        key: "name"
    },
    {
        label: "更新人：",
        key: "name"
    },
    {
        label: "更新时间：",
        key: "name"
    }
]
const info = ref({})
const recordType = ref("operate")

const dataSource = ref([])
const pagination = ref({
    pageNo: 1,
    pageSize: 10,
    total: 0
})

const columns = ref([
    { title: "卡号", dataIndex: "index", width: 100 },
    { title: "卡片操作", dataIndex: "majorName", key: "majorName", width: 100 },
    { title: "操作详情", dataIndex: "educationalSystem", key: "educationalSystem", width: 100 },
    { title: "操作人", dataIndex: "createBy", key: "createBy", width: 100 },
    { title: "操作时间", dataIndex: "createTime", key: "createTime", width: 100 },
    { title: "操作", dataIndex: "operate", width: 140, fixed: "right" }
])

function getList() {
    // http.post("/cloud/enrollment/major/page", { ...pagination.value, ...query.value }).then((res) => {
    //     dataSource.value = res.data?.list
    //     pagination.value.pageNo = res.data?.pageNo
    //     pagination.value.pageSize = res.data?.pageSize
    //     pagination.value.total = res.data?.total
    // })
    dataSource.value = [{ id: 1, index: 1, majorName: "1" }]
}

function handleTableChange({ current, pageSize, total }) {
    pagination.value.pageNo = current
    pagination.value.pageSize = pageSize
    pagination.value.total = total
    getList()
}

function getInitList() {
    pagination.value.pageNo = 1
    pagination.value.pageSize = 10
    getList()
}

function cardRecordInfoFn() {
    console.log("cardRecordInfoFn")
}

function open(item) {
    drawerOpen.value = true
    info.value = item || {}
    getList()
}
function cancel() {
    drawerOpen.value = false
}
defineExpose({ open })
</script>

<style lang="less" scoped>
.drawer_content {
    max-width: 1200px;
    margin: auto;
    .title {
        display: flex;
        align-items: center;
        margin-bottom: 14px;

        .line {
            width: 2px;
            height: 14px;
            background: var(--primary-color);
            margin-right: 4px;
        }
        font-weight: 500;
        font-size: 14px;
        color: var(--text-color);
        line-height: 20px;
    }
    .record {
        padding: 16px 0;
        margin: 16px 0;
        border-top: 1px dashed #d8d8d8;
    }
}
</style>
