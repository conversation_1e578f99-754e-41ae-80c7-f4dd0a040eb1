{
    "compilerOptions": {
        "target": "ESNext",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "moduleResolution": "node",
        "strict": true,
        "jsx": "preserve",
        "sourceMap": true,
        "resolveJsonModule": true,
        "isolatedModules": false,
        "esModuleInterop": true,
        "lib": ["ESNext", "DOM"],
        "skipLibCheck": true,
        "allowJs": true,
        "paths": {
            "@/*": ["./src/*"]
        },
        "types": ["vite/client", "vite-plugin-svgr/client"],        
        "allowSyntheticDefaultImports": true,
    },
    "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
    "exclude": ["config/index.js"],
    "types": ["ant-design-vue/typings/global"]
}
