<template>
    <a-drawer :width="`calc(100% - ${!collapsed ? '87px' : '215px'} )`" :closable="false" v-model:open="drawerOpen" :maskClosable="false" :keyboard="false" :mask="false" rootClassName="YDrawer" :destroyOnClose="true" :rootStyle="rootStyle" v-bind="$attrs" @close="close">
        <template #title>
            <div class="drawer_title">
                <div>
                    <ArrowLeftOutlined @click="close" :style="{ color: 'var(--primary-color)', fontSize: '16px' }" />
                    <span class="page_title">{{ title }}</span>
                </div>
                <slot name="title"></slot>
            </div>
        </template>
        <template #footer>
            <slot name="footer"></slot>
        </template>
        <slot></slot>
    </a-drawer>
</template>
<script setup>
import useStore from "@/store"

defineOptions({
    inheritAttrs: false
})

const props = defineProps({
    open: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ""
    },
    rootStyle: {
        type: Object,
        default: () => {
            return {}
        }
    },
    backMessage: {
        type: String,
        default: ""
    }
})

const rootStyle = computed(() => {
    return {
        height: "calc(100vh - 106px)",
        top: "106px",
        ...props.rootStyle
    }
})

const emit = defineEmits(["update:open", "close"])

const drawerOpen = ref(false)
watch(
    () => props.open,
    (val) => {
        drawerOpen.value = val
    },
    {
        immediate: true,
        deep: true
    }
)

const collapsed = computed(() => {
    const { system } = useStore()
    return system.collapsed === "open"
})

const close = async () => {
    drawerOpen.value = false
    emit("update:open", drawerOpen.value)
    emit("close")
}
</script>
<style lang="less">
.YDrawer {
    outline: none;
}
.YDrawer .ant-drawer-content-wrapper {
    box-shadow: none;
    .ant-drawer-header {
        padding-top: 0px;
        padding-bottom: 0px;
    }
    .ant-drawer-header {
        border-bottom: none;
    }
    .drawer_title {
        height: 58px;
        padding-left: 24px;
        margin-top: -4px;
        margin-left: -24px;
        margin-right: -24px;
        font-size: 16px;
        color: #000;
        font-weight: 600;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        flex: 1;
        .page_title {
            margin-left: 10px;
        }
    }
}
.YDrawer {
    outline: none;
}
</style>
