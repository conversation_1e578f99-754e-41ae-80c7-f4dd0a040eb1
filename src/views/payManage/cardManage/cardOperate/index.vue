<template>
    <div class="card_operate">
        <div class="card_operate_head">
            <ArrowLeftOutlined @click="back" :style="{ color: 'var(--primary-color)', fontSize: '16px' }" />
            <span class="page_title">卡号操作</span>
        </div>
        <div class="page_content">
            <a-radio-group style="margin-bottom: 12px" v-model:value="comType" button-style="solid" @change="getList">
                <a-radio-button value="declareLost">
                    <div class="radio_item">挂失</div>
                </a-radio-button>
                <a-radio-button value="removeLostDeclare">
                    <div class="radio_item">解挂</div>
                </a-radio-button>
                <a-radio-button value="newCard">
                    <div class="radio_item">补办/换卡</div>
                </a-radio-button>
                <a-radio-button value="logOutCard">
                    <div class="radio_item">注销</div>
                </a-radio-button>
            </a-radio-group>
            <component :is="operateCom" ref="operateComRef"></component>
        </div>
    </div>
</template>

<script setup>
import LogOutCard from "./components/logOutCard.vue"
import DeclareLost from "./components/declareLost.vue"
import ReplacementCard from "./components/replacementCard.vue"
import RemoveLostDeclare from "./components/removeLostDeclare.vue"

import { useRouter } from "vue-router"
import { nextTick } from "vue"
const router = useRouter()
const comType = ref("declareLost")
const operateComRef = ref(null)

const operateCom = computed(() => {
    const com = {
        declareLost: shallowRef(DeclareLost),
        removeLostDeclare: shallowRef(RemoveLostDeclare),
        newCard: shallowRef(ReplacementCard),
        logOutCard: shallowRef(LogOutCard)
    }
    return com[comType.value]?.value
})

function getList() {
    nextTick(() => {
        operateComRef.value?.reset()
    })
}
onMounted(() => {
    getList()
})
function back() {
    router.back()
}
</script>

<style lang="less" scoped>
.card_operate {
    .card_operate_head {
        padding: 18px 20px;
        border-bottom: 1px solid #d9d9d9;
        display: flex;
        align-items: center;
        flex: 1;
        .page_title {
            margin-left: 10px;
            font-weight: 600;
            font-size: 18px;
            color: var(--text-color);
            line-height: 25px;
        }
    }
    .page_content {
        padding: 20px;
        .radio_item {
            min-width: 100px;
            text-align: center;
        }
    }
}
</style>
