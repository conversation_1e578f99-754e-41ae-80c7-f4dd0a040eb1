<template>
    <div text-right v-if="show" mt-20>
        <a-pagination class="yd_pagination_box" :pageSizeOptions="pageSizeOptions" :showQuickJumper="showQuickJumper" show-less-items :size="size" :showSizeChanger="showSizeChanger" :total="total" :current="current" v-model:pageSize="state.pageSize" @change="paginationChange" :show-total="(total) => `共 ${total} 条`" />
    </div>
</template>
<script setup>
const props = defineProps({
    total: {
        type: [String, Number],
        default: 0
    },
    current: {
        type: Number,
        default: 1
    },
    pageSize: {
        type: Number,
        default: 10
    },
    showQuickJumper: {
        type: Boolean,
        default: true
    },
    showSizeChanger: {
        type: Boolean,
        default: true
    },
    size: {
        type: String,
        default: ""
    }
})

const pageSizeOptions = ["10", "20", "30", "40", "50"]

// *********************
// Hooks Function
// *********************
const emit = defineEmits(["paginationChange"])

const show = ref(false)
const state = reactive({
    pageSize: 10
})

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const paginationChange = (pageNo, pageSize) => {
    emit("paginationChange", { pageNo, pageSize })
}

// *********************
// Watch Function
// *********************

watch(
    () => props.pageSize,
    (val) => {
        state.pageSize = val
    },
    {
        immediate: true
    }
)

watch(
    () => props.total,
    (total) => {
        show.value = total > 10 ? true : false
    },
    {
        immediate: true
    }
)
</script>
<style lang="less" scoped>
.yd_pagination_box {
    :deep(.ant-pagination-item) {
        border: 1px solid #d9d9d9;
    }
    :deep(.ant-pagination-item-active) {
        background-color: var(--primary-color);
        a {
            color: #fff;
        }
    }
}
</style>
