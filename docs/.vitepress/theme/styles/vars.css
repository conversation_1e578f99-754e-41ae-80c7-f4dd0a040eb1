/**
 * Colors
 * -------------------------------------------------------------------------- */

 :root {
    --vt-c-green: #42b883;
    --vt-c-green-light: #42d392;
    --vt-c-green-lighter: #35eb9a;
    --vt-c-green-dark: #33a06f;
    --vt-c-green-darker: #155f3e;
    --vt-c-blue: #3b8eed;
    --vt-c-blue-light: #549ced;
    --vt-c-blue-lighter: #50a2ff;
    --vt-c-blue-dark: #3468a3;
    --vt-c-blue-darker: #255489;
    --vp-c-brand-dimm: rgba(100, 108, 255, 0.08);
  }
  
  /**
   * Component: Button
   * -------------------------------------------------------------------------- */
  
  :root {
    --vp-button-brand-border: var(--vp-c-brand-light);
    --vp-button-brand-text: var(--vp-c-text-dark-1);
    --vp-button-brand-bg: var(--vp-c-brand);
    --vp-button-brand-hover-border: var(--vp-c-brand-light);
    --vp-button-brand-hover-text: var(--vp-c-text-dark-1);
    --vp-button-brand-hover-bg: var(--vp-c-brand-light);
    --vp-button-brand-active-border: var(--vp-c-brand-light);
    --vp-button-brand-active-text: var(--vp-c-text-dark-1);
    --vp-button-brand-active-bg: var(--vp-button-brand-bg);
  }
  
  /**
   * Component: Home
   * -------------------------------------------------------------------------- */
  
  :root {
    --vp-home-hero-name-color: transparent;
    --vp-home-hero-name-background: -webkit-linear-gradient(315deg, #42d392 25%, #647eff);
    --vp-home-hero-image-background-image: -webkit-linear-gradient(315deg, #42d392 25%, #647eff);
    --vp-home-hero-image-filter: blur(40px);
  }
  
  @media (min-width: 640px) {
    :root {
      --vp-home-hero-image-filter: blur(56px);
    }
  }
  
  @media (min-width: 960px) {
    :root {
      --vp-home-hero-image-filter: blur(72px);
    }
  }
  
  /**
   * Component: Custom Block
   * -------------------------------------------------------------------------- */
  
  :root {
    --vp-custom-block-tip-border: var(--vp-c-brand);
    --vp-custom-block-tip-text: var(--vp-c-brand-darker);
    --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
    --vp-code-vue-bg: #f1f1f1;
    --vp-code-vue-color: #333;
  }
  
  .dark {
    --vp-custom-block-tip-border: var(--vp-c-brand);
    --vp-custom-block-tip-text: var(--vp-c-brand-lightest);
    --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
    --vp-code-vue-bg: var(--vp-c-bg-alt);
    --vp-code-vue-color: #fff;
  }
  
  /**
   * Component: Algolia
   * -------------------------------------------------------------------------- */
  
  .DocSearch {
    --docsearch-primary-color: var(--vp-c-brand) !important;
  }
  
  /**
   * VitePress: Custom fix
   * -------------------------------------------------------------------------- */
  
  /*
    Use lighter colors for links in dark mode for a11y.
    Also specify some classes twice to have higher specificity
    over scoped class data attribute.
  */
  .dark .vp-doc a,
  .dark .vp-doc a > code,
  .dark .VPNavBarMenuLink.VPNavBarMenuLink:hover,
  .dark .VPNavBarMenuLink.VPNavBarMenuLink.active,
  .dark .link.link:hover,
  .dark .link.link.active,
  .dark .edit-link-button.edit-link-button,
  .dark .pager-link .title {
    color: var(--vp-c-brand-lighter);
  }
  
  .dark .vp-doc a:hover,
  .dark .vp-doc a > code:hover {
    color: var(--vp-c-brand-lightest);
    opacity: 1;
  }
  
  /* Transition by color instead of opacity */
  .dark .vp-doc .custom-block a {
    transition: color 0.25s;
  }